<template>
  <div class="json-table-demo">
    <!-- 控制面板 -->
    <div class="control-panel">
      <!-- JSON数据输入区域 -->
      <div class="json-input-section">
        <h3>JSON数据输入</h3>
        <div class="json-controls">
          <div class="preset-buttons">
            <button @click="loadPresetData('basic')" class="preset-btn basic">基础数据</button>
            <button @click="loadPresetData('merged')" class="preset-btn merged">合并单元格</button>
            <button @click="loadPresetData('complex')" class="preset-btn complex">复杂示例</button>
            <button @click="loadPresetData('customHeader')" class="preset-btn custom">自定义表头</button>
            <button @click="clearJsonInput" class="preset-btn clear">清空</button>
          </div>

          <div class="json-editor">
            <textarea
              v-model="jsonInput"
              placeholder="请输入JSON数据..."
              class="json-textarea"
              rows="12"
            ></textarea>
          </div>

          <div class="json-options">
            <label class="option-item">
              <input type="checkbox" v-model="insertOptions.clearExisting">
              清空现有数据
            </label>
            <label class="option-item">
              <input type="checkbox" v-model="insertOptions.validateData">
              验证数据格式
            </label>
            <div class="option-item">
              <label>开始行:</label>
              <input
                type="number"
                v-model.number="insertOptions.startRow"
                min="0"
                class="number-input"
              >
            </div>
          </div>

          <div class="action-buttons">
            <button @click="insertJsonData" class="action-btn insert" :disabled="!jsonInput.trim()">
              插入数据
            </button>
            <button @click="exportJsonData" class="action-btn export">
              导出JSON
            </button>
            <button @click="validateJson" class="action-btn validate">
              验证格式
            </button>
            <button @click="exportToWord" class="action-btn word" :disabled="!hasTableData">
              导出Word
            </button>
          </div>
        </div>
      </div>

      <!-- 操作结果显示 -->
      <div class="result-section" v-if="operationResult">
        <h4>操作结果</h4>
        <div class="result-content" :class="operationResult.success ? 'success' : 'error'">
          <p><strong>状态:</strong> {{ operationResult.success ? '成功' : '失败' }}</p>
          <p><strong>消息:</strong> {{ operationResult.message }}</p>
          <p v-if="operationResult.insertedRows"><strong>插入行数:</strong> {{ operationResult.insertedRows }}</p>
          <pre v-if="operationResult.error" class="error-details">{{ operationResult.error }}</pre>
        </div>
      </div>
    </div>

    <!-- 表格容器 -->
    <div class="table-section">
      <h3>表格预览</h3>
      <TableContainer
        ref="tableContainer"
        :table-width="'100%'"
        :table-height="'500px'"
        :data-rows="tableData"
        @data-inserted="handleDataInserted"
        @table-updated="handleTableUpdated"
      />
    </div>

    <!-- JSON格式说明 -->
    <div class="help-section">
      <h3>JSON数据格式说明</h3>
      <div class="help-content">
        <div class="format-example">
          <h4>基本格式:</h4>
          <pre class="code-block">{
  "rows": [
    ["单元格1", "单元格2", "单元格3", "单元格4", "单元格5", "单元格6", "单元格7", "单元格8"],
    ["数据1", "数据2", "数据3", "数据4", "数据5", "数据6", "数据7", "数据8"]
  ],
  "merges": [
    {
      "startRow": 0,
      "startCol": 0,
      "endRow": 1,
      "endCol": 0,
      "content": "合并内容"
    }
  ]
}</pre>
        </div>

        <div class="format-rules">
          <h4>格式规则:</h4>
          <ul>
            <li><code>rows</code>: 必需，包含表格行数据的数组</li>
            <li>每行最多8个单元格，对应表格的8列</li>
            <li><code>merges</code>: 可选，单元格合并配置数组</li>
            <li>合并配置包含起始行列和结束行列坐标</li>
            <li>支持数学公式，使用LaTeX语法，如: <code>$x^2 + y^2 = r^2$</code></li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import TableContainer from '@/components/TableContainer.vue'
import mathFormulaUtils from '@/utils/math-formula-utils'

export default {
  name: 'JsonTableDemo',
  components: {
    TableContainer
  },
  data() {
    return {
      // JSON输入
      jsonInput: '',

      // 插入选项
      insertOptions: {
        clearExisting: true,
        validateData: true,
        startRow: 0
      },

      // 表格数据
      tableData: [
        Array(8).fill(null).map(() => ({
          content: '',
          isEditing: false,
          originalContent: '',
          hasMath: false
        }))
      ],

      // 操作结果
      operationResult: null,

      // 预设数据
      presetData: {
        basic: {
          rows: [
            ["检查项目1", "技术要求1", "合格", "12", "15", "张三", "李四", "王五"],
            ["检查项目2", "技术要求2", "合格", "12", "16", "张三", "李四", "王五"],
            ["检查项目3", "技术要求3", "不合格", "12", "17", "张三", "李四", "王五"]
          ]
        },
        merged: {
          rows: [
            ["外观检查", "表面无划痕、无变形", "合格", "12", "15", "张三", "李四", "王五"],
            ["", "尺寸符合图纸要求", "合格", "12", "16", "张三", "李四", "王五"],
            ["功能测试", "各项功能正常", "合格", "12", "17", "张三", "李四", "王五"]
          ],
          merges: [
            {
              startRow: 0,
              startCol: 0,
              endRow: 1,
              endCol: 0,
              content: "外观检查"
            }
          ]
        },
        complex: {
          rows: [
            ["数学公式测试", "$E = mc^2$", "合格", "12", "15", "张三", "李四", "王五"],
            ["几何计算", "$\\pi r^2$", "合格", "12", "16", "张三", "李四", "王五"],
            ["统计分析", "$\\sum_{i=1}^n x_i$", "合格", "12", "17", "张三", "李四", "王五"],
            ["积分计算", "$\\int_0^1 x dx = \\frac{1}{2}$", "合格", "12", "18", "张三", "李四", "王五"]
          ],
          merges: [
            {
              startRow: 2,
              startCol: 3,
              endRow: 3,
              endCol: 4,
              content: "12月17-18日"
            }
          ]
        },
        customHeader: {
          // 自定义表头配置
          headerConfig: {
            headers: [
              ['产品名称', '规格型号', '质量检验', '生产信息', '', '责任人员', '', ''],
              ['', '', '', '批次号', '日期', '检验员', '审核员', '负责人']
            ],
            merges: [
              // 产品名称 - 纵向合并
              { startRow: 0, startCol: 0, endRow: 1, endCol: 0, content: '产品名称' },
              // 规格型号 - 纵向合并
              { startRow: 0, startCol: 1, endRow: 1, endCol: 1, content: '规格型号' },
              // 质量检验 - 纵向合并
              { startRow: 0, startCol: 2, endRow: 1, endCol: 2, content: '质量检验' },
              // 生产信息 - 横向合并
              { startRow: 0, startCol: 3, endRow: 0, endCol: 4, content: '生产信息' },
              // 责任人员 - 横向合并
              { startRow: 0, startCol: 5, endRow: 0, endCol: 7, content: '责任人员' }
            ]
          },
          // 自定义表头宽度配置
          headerWidthConfig: {
            columnWidths: [180, 150, 120, 100, 100, 90, 90, 90], // 8列的宽度
            headerHeights: [60, 40] // 两行表头的高度
          },
          // 数据行（使用复杂格式支持公式和宽度）
          cellRows: [
            [
              { content: "智能手机", width: 180, height: 50 },
              { content: "iPhone 15 Pro", width: 150, height: 50 },
              { content: "外观检查：无划痕", width: 120, height: 50 },
              { content: "A001", width: 100, height: 50 },
              { content: "2024-01-15", width: 100, height: 50 },
              { content: "张三", width: 90, height: 50 },
              { content: "李四", width: 90, height: 50 },
              { content: "王五", width: 90, height: 50 }
            ],
            [
              { content: "", width: 180, height: 50 },
              { content: "", width: 150, height: 50 },
              { content: "功能测试：正常", width: 120, height: 50 },
              { content: "A001", width: 100, height: 50 },
              { content: "2024-01-15", width: 100, height: 50 },
              { content: "张三", width: 90, height: 50 },
              { content: "李四", width: 90, height: 50 },
              { content: "王五", width: 90, height: 50 }
            ],
            [
              { content: "平板电脑", width: 180, height: 50 },
              { content: "iPad Air", width: 150, height: 50 },
              { content: "性能测试：$CPU = 95\\%$", hasMath: true, width: 120, height: 60 },
              { content: "B002", width: 100, height: 50 },
              { content: "2024-01-16", width: 100, height: 50 },
              { content: "赵六", width: 90, height: 50 },
              { content: "钱七", width: 90, height: 50 },
              { content: "孙八", width: 90, height: 50 }
            ]
          ],
          // 数据行合并配置
          merges: [
            {
              startRow: 0,
              startCol: 0,
              endRow: 1,
              endCol: 0,
              content: "智能手机\n（多功能检测）"
            }
          ],
          // 元数据
          metadata: {
            title: "产品质量检验记录表",
            useDynamicHeader: true,
            hasCustomWidth: true
          }
        }
      }
    }
  },
  computed: {
    // 检查是否有表格数据
    hasTableData() {
      return this.tableData && this.tableData.length > 0 &&
             this.tableData.some(row =>
               row.some(cell => cell.content && cell.content.trim() !== '')
             )
    }
  },
  mounted() {
    // 加载默认数据
    this.loadPresetData('basic')
  },
  methods: {
    /**
     * 加载预设数据
     */
    loadPresetData(type) {
      const preset = this.presetData[type]
      if (preset) {
        this.jsonInput = JSON.stringify(preset, null, 2)
        this.operationResult = null
      }
    },

    /**
     * 清空JSON输入
     */
    clearJsonInput() {
      this.jsonInput = ''
      this.operationResult = null
    },

    /**
     * 验证JSON格式
     */
    validateJson() {
      try {
        if (!this.jsonInput.trim()) {
          this.operationResult = {
            success: false,
            message: 'JSON输入为空'
          }
          return
        }

        const jsonData = JSON.parse(this.jsonInput)
        const tableContainer = this.$refs.tableContainer

        if (tableContainer && typeof tableContainer.validateJSONData === 'function') {
          const isValid = tableContainer.validateJSONData(jsonData)
          this.operationResult = {
            success: isValid,
            message: isValid ? 'JSON格式验证通过' : 'JSON格式不正确'
          }
        } else {
          this.operationResult = {
            success: true,
            message: 'JSON语法正确'
          }
        }
      } catch (error) {
        this.operationResult = {
          success: false,
          message: 'JSON格式错误',
          error: error.message
        }
      }
    },

    /**
     * 插入JSON数据
     */
    insertJsonData() {
      try {
        if (!this.jsonInput.trim()) {
          this.operationResult = {
            success: false,
            message: 'JSON输入为空'
          }
          return
        }

        const jsonData = JSON.parse(this.jsonInput)
        const tableContainer = this.$refs.tableContainer

        if (!tableContainer) {
          this.operationResult = {
            success: false,
            message: '表格组件未找到'
          }
          return
        }

        // 检查是否有自定义表头配置
        if (jsonData.headerConfig && jsonData.headerWidthConfig) {
          // 使用方法设置动态表头配置（避免直接修改props）
          tableContainer.setDynamicHeaderConfig(
            true,
            jsonData.headerConfig,
            jsonData.headerWidthConfig
          )

          console.log('应用自定义表头配置:', {
            headerConfig: jsonData.headerConfig,
            headerWidthConfig: jsonData.headerWidthConfig
          })
        } else {
          // 使用默认表头
          tableContainer.setDynamicHeaderConfig(false, null, null)
        }

        // 准备合并单元格配置
        const mergeCells = jsonData.merges || []
        const options = {
          ...this.insertOptions,
          mergeCells
        }

        // 调用插入方法（优先使用cellRows格式）
        const insertData = jsonData.cellRows ? { cellRows: jsonData.cellRows } : jsonData
        const result = tableContainer.insertDataFromJSON(insertData, options)
        this.operationResult = result

        // 更新本地表格数据引用
        if (result.success) {
          this.tableData = tableContainer.dataRows
        }

      } catch (error) {
        this.operationResult = {
          success: false,
          message: '数据插入失败',
          error: error.message
        }
      }
    },

    /**
     * 导出JSON数据
     */
    exportJsonData() {
      const tableContainer = this.$refs.tableContainer
      if (!tableContainer) {
        this.operationResult = {
          success: false,
          message: '表格组件未找到'
        }
        return
      }

      try {
        const jsonData = tableContainer.getDataAsJSON({
          includeEmpty: false,
          includeMergeInfo: true
        })

        this.jsonInput = JSON.stringify(jsonData, null, 2)
        this.operationResult = {
          success: true,
          message: `成功导出 ${jsonData.rows.length} 行数据`
        }
      } catch (error) {
        this.operationResult = {
          success: false,
          message: '数据导出失败',
          error: error.message
        }
      }
    },

    /**
     * 导出Word文档
     */
    async exportToWord() {
      if (!this.hasTableData) {
        this.operationResult = {
          success: false,
          message: '表格中没有数据，请先插入一些内容'
        }
        return
      }

      // 显示加载提示
      const loading = this.$loading({
        lock: true,
        text: '正在生成Word文档，请稍候...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      try {
        console.log('开始导出Word文档...')

        // 准备导出数据（包含合并单元格信息）
        const exportData = await this.prepareExportDataWithMerges()
        console.log('导出数据:', exportData)

        // 动态导入API
        const { exportTableToWord } = await import('@/api/word/export')

        // 调用导出API
        const response = await exportTableToWord(exportData)

        console.log('API响应:', response)

        // 检查响应
        if (!response || !response.data) {
          throw new Error('服务器返回数据为空')
        }

        if (response.status !== 200) {
          throw new Error(`服务器响应错误: ${response.status}`)
        }

        const dataSize = response.data.size || response.data.byteLength || response.data.length
        if (!dataSize || dataSize === 0) {
          throw new Error('返回的文件数据为空')
        }

        // 处理文件下载
        this.downloadWordFile(response, '检验记录表（含合并单元格）')

        loading.close()
        this.operationResult = {
          success: true,
          message: 'Word文档导出成功！'
        }

      } catch (error) {
        loading.close()
        console.error('导出Word文档失败:', error)

        let errorMessage = '导出失败'
        if (error.response) {
          if (error.response.status === 404) {
            errorMessage = '导出服务不可用，请检查后端服务'
          } else if (error.response.status === 500) {
            errorMessage = '服务器内部错误，请稍后重试'
          } else {
            errorMessage = `服务器错误: ${error.response.status}`
          }
        } else if (error.message) {
          errorMessage = `导出失败: ${error.message}`
        }

        this.operationResult = {
          success: false,
          message: errorMessage,
          error: error.message
        }
      }
    },

    /**
     * 准备包含合并单元格信息的导出数据
     */
    async prepareExportDataWithMerges() {
      const tableContainer = this.$refs.tableContainer
      if (!tableContainer) {
        throw new Error('表格组件未找到')
      }

      // 获取完整的表格数据（包含合并信息和表头配置）
      const tableData = tableContainer.getDataAsJSON({
        includeEmpty: false,
        includeMergeInfo: true
      })

      // 使用TableContainer中的当前表头配置
      const headers = tableData.headers || [
        // 默认表头（如果没有动态配置）
        ['检查项目', '技术要求', '检查结果', '完工', '', '检查员', '组长', '检验员'],
        ['', '', '', '月', '日', '', '', '']
      ]

      // 使用TableContainer中的当前表头合并配置
      const headerMerges = tableData.headerMerges || [
        // 默认表头合并配置（如果没有动态配置）
        { startRow: 0, startCol: 0, endRow: 1, endCol: 0, content: '检查项目' },
        { startRow: 0, startCol: 1, endRow: 1, endCol: 1, content: '技术要求' },
        { startRow: 0, startCol: 2, endRow: 1, endCol: 2, content: '检查结果' },
        { startRow: 0, startCol: 3, endRow: 0, endCol: 4, content: '完工' },
        { startRow: 0, startCol: 5, endRow: 1, endCol: 5, content: '检查员' },
        { startRow: 0, startCol: 6, endRow: 1, endCol: 6, content: '组长' },
        { startRow: 0, startCol: 7, endRow: 1, endCol: 7, content: '检验员' }
      ]

      // 使用TableContainer中的当前表头宽度配置
      const headerWidthConfig = tableData.headerWidthConfig || {
        columnWidths: [150, 200, 150, 50, 50, 80, 80, 80],
        headerHeights: [50, 50]
      }

      // 获取表头纵向文字配置
      const verticalHeadersConfig = tableContainer.verticalHeaders || [false, false, false, false, false, true, true, true]

      console.log('使用的表头配置:', {
        headers,
        headerMerges,
        headerWidthConfig,
        verticalHeadersConfig,
        useDynamicHeader: tableData.metadata?.useDynamicHeader
      })

      // 处理数据行，转换LaTeX公式为MathML（传递正确的宽度配置）
      const cellRows = await this.processTableDataWithLatex(tableData.rows, headerWidthConfig)

      // 构建导出数据结构
      const exportData = {
        title: tableData.metadata?.title || '检验记录表',
        headers: headers,
        cellRows: cellRows,              // 使用处理后的复杂格式
        rows: tableData.rows,            // 保留简单格式作为备用
        merges: tableData.merges || [], // 数据行的合并
        headerMerges: headerMerges,     // 表头的合并
        headerWidthConfig: headerWidthConfig, // 表头宽度配置
        verticalHeadersConfig: verticalHeadersConfig, // 表头纵向文字配置
        metadata: {
          exportTime: new Date().toISOString(),
          totalRows: tableData.rows.length,
          totalColumns: headers[0].length, // 使用第一行表头的列数
          headerRows: headers.length,       // 表头行数
          hasMergedCells: (tableData.merges || []).length > 0,
          hasHeaderMerges: headerMerges.length > 0,
          hasLatexProcessing: true,        // 标记已进行LaTeX处理
          useDynamicHeader: tableData.metadata?.useDynamicHeader || false,
          hasCustomWidth: headerWidthConfig && headerWidthConfig.columnWidths,
          hasVerticalHeaders: verticalHeadersConfig && verticalHeadersConfig.some(v => v === true)
        }
      }

      console.log('准备的导出数据:', exportData)
      return exportData
    },

    /**
     * 处理表格数据，转换LaTeX公式为MathML
     */
    async processTableDataWithLatex(rows, headerWidthConfig = null) {
      console.log('开始处理表格数据，转换LaTeX公式，宽度配置:', headerWidthConfig)

      try {
        // 确保MathJax已初始化
        await mathFormulaUtils.initializeMathJax()

        const cellRows = []

        for (let rowIndex = 0; rowIndex < rows.length; rowIndex++) {
          const row = rows[rowIndex]
          const cellRow = []

          for (let colIndex = 0; colIndex < row.length; colIndex++) {
            const cellContent = row[colIndex]?.content || row[colIndex] || ''

            try {
              // 构建基础CellData对象
              const cellData = {
                content: cellContent,
                hasMath: false,
                mathML: null,
                hasMultipleContent: false,
                mathMLMap: null,
                width: this.getCellWidthFromConfig(rowIndex, colIndex, headerWidthConfig),
                height: this.getCellHeightFromConfig(rowIndex, colIndex, headerWidthConfig)
              }

              // 检查是否包含数学公式
              if (mathFormulaUtils.containsMath(cellContent)) {
                console.log(`单元格(${rowIndex},${colIndex})包含公式:`, cellContent)

                // 分离公式和普通文本
                const separationResult = mathFormulaUtils.separateFormulaAndText(cellContent)

                if (separationResult.hasFormula) {
                  // 处理混合内容（文本 + 公式）
                  cellData.hasMath = true
                  cellData.hasMultipleContent = true
                  cellData.content = separationResult.processedContent

                  // 转换所有公式为MathML
                  const mathMLMap = {}
                  for (const formula of separationResult.formulas) {
                    try {
                      const mathML = await mathFormulaUtils.latexToMathML(formula.content)
                      if (mathML) {
                        mathMLMap[formula.placeholder] = mathML
                        console.log(`公式转换成功: ${formula.content} -> MathML`)
                      }
                    } catch (error) {
                      console.warn(`公式转换失败: ${formula.content}`, error)
                    }
                  }

                  cellData.mathMLMap = mathMLMap

                } else {
                  // 处理纯公式内容
                  const mathML = await mathFormulaUtils.latexToMathML(cellContent)
                  if (mathML) {
                    cellData.hasMath = true
                    cellData.mathML = mathML
                    console.log(`纯公式转换成功: ${cellContent} -> MathML`)
                  }
                }
              }

              cellRow.push(cellData)

            } catch (error) {
              console.error(`处理单元格(${rowIndex},${colIndex})失败:`, error)
              // 处理失败时使用原始内容
              cellRow.push({
                content: cellContent,
                hasMath: false,
                width: this.getCellWidthFromConfig(rowIndex, colIndex, headerWidthConfig),
                height: this.getCellHeightFromConfig(rowIndex, colIndex, headerWidthConfig)
              })
            }
          }

          cellRows.push(cellRow)
        }

        console.log('表格数据处理完成，转换的行数:', cellRows.length)
        return cellRows

      } catch (error) {
        console.error('处理表格数据失败:', error)
        // 如果处理失败，返回简单格式
        return rows.map(row =>
          row.map((cell, colIndex) => ({
            content: cell?.content || cell || '',
            hasMath: false,
            width: this.getCellWidth(0, colIndex),
            height: this.getCellHeight(0, colIndex)
          }))
        )
      }
    },

    /**
     * 获取单元格宽度配置（从TableContainer）
     */
    getCellWidth(rowIndex, colIndex) {
      const tableContainer = this.$refs.tableContainer
      console.log(`getCellWidth调用: 行${rowIndex} 列${colIndex}`)
      console.log('tableContainer存在:', !!tableContainer)

      if (tableContainer && tableContainer.currentHeaderWidthConfig) {
        const widthConfig = tableContainer.currentHeaderWidthConfig
        console.log('当前宽度配置:', widthConfig)

        if (widthConfig.columnWidths && widthConfig.columnWidths[colIndex] !== undefined) {
          const width = widthConfig.columnWidths[colIndex]
          console.log(`获取第${colIndex}列宽度: ${width}像素 (来源: 动态配置)`)
          return width
        } else {
          console.log(`第${colIndex}列在动态配置中未找到`)
        }
      } else {
        console.log('tableContainer或currentHeaderWidthConfig不存在')
      }

      // 默认宽度配置
      const defaultColumnWidths = [120, 200, 80, 60, 60, 80, 80, 80]
      const width = defaultColumnWidths[colIndex] || 150
      console.log(`获取第${colIndex}列宽度: ${width}像素 (来源: 默认配置)`)
      return width
    },

    /**
     * 获取单元格宽度配置（从传入的配置）
     */
    getCellWidthFromConfig(rowIndex, colIndex, headerWidthConfig) {
      console.log(`getCellWidthFromConfig调用: 行${rowIndex} 列${colIndex}`)
      console.log('传入的宽度配置:', headerWidthConfig)

      if (headerWidthConfig && headerWidthConfig.columnWidths && headerWidthConfig.columnWidths[colIndex] !== undefined) {
        const width = headerWidthConfig.columnWidths[colIndex]
        console.log(`获取第${colIndex}列宽度: ${width}像素 (来源: 传入配置)`)
        return width
      }

      // 回退到TableContainer配置
      return this.getCellWidth(rowIndex, colIndex)
    },

    /**
     * 获取单元格高度配置（从TableContainer）
     */
    getCellHeight(rowIndex, colIndex) {
      const tableContainer = this.$refs.tableContainer
      if (tableContainer && tableContainer.currentHeaderWidthConfig) {
        const widthConfig = tableContainer.currentHeaderWidthConfig
        if (widthConfig.headerHeights && widthConfig.headerHeights.length > 0) {
          // 对于数据行，使用第一个表头行的高度作为参考
          return widthConfig.headerHeights[0] || 50
        }
      }

      // 默认高度
      return 50
    },

    /**
     * 获取单元格高度配置（从传入的配置）
     */
    getCellHeightFromConfig(rowIndex, colIndex, headerWidthConfig) {
      if (headerWidthConfig && headerWidthConfig.headerHeights && headerWidthConfig.headerHeights.length > 0) {
        // 对于数据行，使用第一个表头行的高度作为参考
        return headerWidthConfig.headerHeights[0] || 50
      }

      // 回退到TableContainer配置
      return this.getCellHeight(rowIndex, colIndex)
    },

    /**
     * 下载Word文件
     */
    downloadWordFile(response, filename = '检验记录表') {
      try {
        // 创建Blob对象
        const blob = new Blob([response.data], {
          type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        })

        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url

        // 设置文件名（添加时间戳避免重复）
        const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')
        link.download = `${filename}_${timestamp}.docx`

        // 触发下载
        document.body.appendChild(link)
        link.click()

        // 清理
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        console.log('文件下载完成:', link.download)
      } catch (error) {
        console.error('文件下载失败:', error)
        throw new Error('文件下载失败: ' + error.message)
      }
    },

    /**
     * 处理数据插入事件
     */
    handleDataInserted(event) {
      console.log('数据插入事件:', event)
    },

    /**
     * 处理表格更新事件
     */
    handleTableUpdated() {
      console.log('表格更新事件')
    }
  }
}
</script>

<style scoped>
.json-table-demo {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
  max-height: 100vh;
  overflow-y: auto;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-header h1 {
  color: #333;
  margin-bottom: 10px;
}

.page-header p {
  color: #666;
  font-size: 16px;
}

.control-panel {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  max-height: 60vh;
  overflow-y: auto;
}

.json-input-section h3 {
  margin-bottom: 15px;
  color: #333;
}

.preset-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.preset-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.preset-btn.basic {
  background: #007bff;
  color: white;
}

.preset-btn.merged {
  background: #28a745;
  color: white;
}

.preset-btn.complex {
  background: #17a2b8;
  color: white;
}

.preset-btn.custom {
  background: #6f42c1;
  color: white;
}

.preset-btn.clear {
  background: #6c757d;
  color: white;
}

.preset-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.json-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.4;
  resize: vertical;
  margin-bottom: 15px;
}

.json-options {
  display: flex;
  gap: 20px;
  align-items: center;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
}

.number-input {
  width: 80px;
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  text-align: center;
}

.action-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.action-btn.insert {
  background: #28a745;
  color: white;
}

.action-btn.export {
  background: #17a2b8;
  color: white;
}

.action-btn.validate {
  background: #ffc107;
  color: #212529;
}

.action-btn.word {
  background: #6f42c1;
  color: white;
}

.action-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.result-section {
  margin-top: 20px;
  padding: 15px;
  border-radius: 4px;
  border: 1px solid #ddd;
}

.result-content.success {
  background: #d4edda;
  border-color: #c3e6cb;
  color: #155724;
}

.result-content.error {
  background: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}

.error-details {
  margin-top: 10px;
  padding: 10px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  font-size: 12px;
  overflow-x: auto;
}

.table-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.table-section h3 {
  margin-bottom: 15px;
  color: #333;
}

.help-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  max-height: 40vh;
  overflow-y: auto;
}

.help-section h3 {
  margin-bottom: 15px;
  color: #333;
}

.help-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.format-example h4,
.format-rules h4 {
  margin-bottom: 10px;
  color: #495057;
}

.code-block {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 15px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  overflow-x: auto;
}

.format-rules ul {
  padding-left: 20px;
}

.format-rules li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.format-rules code {
  background: #f8f9fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

@media (max-width: 768px) {
  .help-content {
    grid-template-columns: 1fr;
  }

  .preset-buttons,
  .json-options,
  .action-buttons {
    flex-direction: column;
    align-items: stretch;
  }

  .preset-btn,
  .action-btn {
    width: 100%;
  }
}
</style>
